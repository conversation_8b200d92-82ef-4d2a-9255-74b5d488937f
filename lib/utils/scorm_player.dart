import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/user_model.dart';
import '../providers/user_provider.dart';
import '../services/scorm_service.dart';
import '../services/logger_service.dart';
import 'lms_server.dart';

class ScormPlayer extends StatefulWidget {
  final String scormDataPath;
  final String moduleName;
  final int moduleId;
  final int courseId;

  const ScormPlayer({super.key, required this.scormDataPath, required this.moduleName, required this.moduleId, required this.courseId});

  @override
  createState() => _ScormPlayerState();
}

class _ScormPlayerState extends State<ScormPlayer> {
  LmsServer? localServer;
  bool isInitialized = false;
  bool isRotated = false;
  final ScormService _scormService = ScormService();

  @override
  void initState() {
    super.initState();
    print("\n----------------------------\n DATA PATH: ${widget.scormDataPath}\n-------------------------------\n");
    _initDBHelper();
  }


  Future<void> _initDBHelper() async {
    try {
      getExternalStorageDirectories().then((value) {
        if (value != null) {
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.landscapeLeft,
            DeviceOrientation.landscapeRight,
          ]).then((orientationValue) {
            setState(() {
              localServer = LmsServer(context, value);
              if (localServer != null) {
                localServer!.initializeServer().then((value) {
                  isInitialized = true;
                });
              }
            });
          });
        }
      });

    } catch (e) {
      if (kDebugMode) {
        print('Database Initialization Error: $e');
      }
    }
  }

    @override
    void dispose() {
      if (isInitialized == true) {
        localServer!.stopServer(force: true);
      }
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]).then((orientationValue) {});
      super.dispose();
    }

    Future<String> retrieveModuleData() async {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;

        if (user == null) {
          LoggerService.warning('No user found for SCORM data retrieval');
          return "";
        }

        // Get SCORM data from local database
        final scormData = await _scormService.getScormDataForModule(user.id, widget.moduleId);

        LoggerService.debug('Retrieved SCORM data for module ${widget.moduleId}: ${scormData.length} characters');

        // Check if the data contains the corrupted suspend_data
        if (scormData.contains('"cmi.suspend_data":"}"')) {
          LoggerService.warning('SCORM: Detected corrupted suspend_data in retrieved data');
        }

        // ✅ Base64 encode the SCORM data
        final encoded = base64Encode(utf8.encode(scormData));

        LoggerService.debug('Encoded SCORM data (base64): ${encoded.substring(0, 100)}...');

        return encoded;
      } catch (e) {
        LoggerService.error('Error retrieving SCORM module data', e);
        return "";
      }
    }


    WebViewController getController(BuildContext context) {

      WebViewController controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (url) {
              if (kDebugMode) {
                print('Page Started $url');
              }
            },
            onNavigationRequest: (navigation) {
              if (kDebugMode) {
                print('Navigating to $navigation');
              }
              return NavigationDecision.navigate;
            },
          ),
        )
        ..addJavaScriptChannel(
          'messageHandler',
          onMessageReceived: (JavaScriptMessage message) async {
            try {
              Map<String, dynamic> cmiData = jsonDecode(message.message);
              final userProvider = Provider.of<UserProvider>(context, listen: false);
              final user = userProvider.user;

              if (user == null) {
                LoggerService.warning('No user found for SCORM data saving');
                return;
              }

              // Save SCORM progress using the service
              final success = await _scormService.saveProgress(
                userId: user.id,
                moduleId: widget.moduleId,
                cmiData: cmiData,
              );

              if (success) {
                LoggerService.debug('SCORM progress saved for module ${widget.moduleId}');

                // Check if module is completed
                final lessonStatus = cmiData['cmi.core.lesson_status'];
                if (lessonStatus == 'completed' || lessonStatus == 'passed') {
                  LoggerService.info('Module ${widget.moduleId} marked as completed');
                  // You can add additional completion logic here if needed
                }
              } else {
                LoggerService.error('Failed to save SCORM progress for module ${widget.moduleId}');
              }
            } catch (error) {
              LoggerService.error('Error processing SCORM message', error);
            }
          },
        );
      return controller;
    }

    // Future<void> markModuleComplete(int userId, int courseId, int moduleId) async {
    //   print("************************\nsaving completion !!\n******************************");
    //   List<UserCourseProgress> progressList = await dbHelper.getUserProgress(userId);
    //
    //   UserCourseProgress? progress = progressList.firstWhere(
    //         (p) => p.courseId == courseId,
    //     orElse: () => UserCourseProgress(userId: userId, courseId: courseId, completedModules: []),
    //   );
    //
    //   if (!progress.completedModules.contains(moduleId)) {
    //     progress.completedModules.add(moduleId);
    //     await dbHelper.saveProgress(progress);
    //   }
    // }


    @override
    Widget build(BuildContext context) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.user;

      if (isInitialized == false) {
        return Container();
      } else {
        var controller = getController(context);
        DefaultAssetBundle.of(context)
            .loadString('assets/scorm_loader.html', cache: false)
            .then((value) async {
          String htmlDocument = value;

          htmlDocument = htmlDocument.replaceFirst('{{SCORM_DATA_PATH}}',widget.scormDataPath);
          htmlDocument = htmlDocument.replaceFirst('{{SCORM_ID}}',"${widget.moduleName}_${user?.id}");

          var resumePoint = await retrieveModuleData();
          print("*****\n*****\n resumePoint: $resumePoint \n*******\n*****");

          htmlDocument = htmlDocument.replaceFirst('{{RESUME_POINT}}', "");

          controller.loadHtmlString(htmlDocument,baseUrl: 'http://localhost:${localServer!.port}/');
        });

        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

        return Scaffold(
          body: Container(
            color: Theme.of(context).primaryColor,
            child: WebViewWidget(
              controller: controller,
            ),
          ),
        );
      }
    }
  }
