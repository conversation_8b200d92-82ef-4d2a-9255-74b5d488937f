import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../config/app_config.dart';
import '../../services/logger_service.dart';
import '../../localization/app_localizations_extension.dart';

class UniversalDownloadPage extends StatefulWidget {
  final String courseSlug;
  final String moduleSlug;
  final String downloadLink;
  final String moduleName;

  const UniversalDownloadPage({
    Key? key,
    required this.courseSlug,
    required this.moduleSlug,
    required this.downloadLink,
    required this.moduleName,
  }) : super(key: key);

  @override
  _UniversalDownloadPageState createState() => _UniversalDownloadPageState();
}

class _UniversalDownloadPageState extends State<UniversalDownloadPage> {
  final Dio _dio = Dio();
  bool _cancelRequested = false;
  double _downloadProgress = 0.0;
  String _downloadedFilePath = '';
  String _status = 'Preparing download...';

  @override
  void initState() {
    super.initState();
    _downloadFile();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Downloading ${widget.moduleName}'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _cancelRequested = true;
            Navigator.pop(context, false);
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.download,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            Text(
              _status,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            LinearProgressIndicator(
              value: _downloadProgress,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 10),
            Text(
              '${(_downloadProgress * 100).toStringAsFixed(1)}%',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                _cancelRequested = true;
                Navigator.pop(context, false);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Cancel Download'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadFile() async {
    try {
      // Use iOS/Android compatible storage
      final appDocDir = await getApplicationDocumentsDirectory();
      final coursePath = '/my_courses/${widget.courseSlug}/${widget.moduleSlug}';
      final downloadUrl = AppConfig.apiBaseUrl + widget.downloadLink;

      setState(() {
        _status = 'Starting download...';
      });

      // Create the target directory
      final targetDir = Directory('${appDocDir.path}$coursePath');
      if (!targetDir.existsSync()) {
        targetDir.createSync(recursive: true);
      }

      // Extract filename from URL or use a default name
      String fileName = _extractFileName(downloadUrl);
      final filePath = '${targetDir.path}/$fileName';

      setState(() {
        _status = 'Downloading $fileName...';
      });

      // Download the file using Dio
      final response = await _dio.download(
        downloadUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (_cancelRequested) {
            throw DioError(
              requestOptions: RequestOptions(path: downloadUrl),
              error: 'Download canceled by user.',
              type: DioErrorType.cancel,
            );
          } else {
            // Calculate download percentage
            final progress = total != -1 ? (received / total) : 0;
            setState(() {
              _downloadProgress = progress.toDouble();
            });
          }
        },
      );

      _downloadedFilePath = filePath;

      setState(() {
        _status = 'Download completed!';
        _downloadProgress = 1.0;
      });

      LoggerService.info('Successfully downloaded file: $fileName');

      // Wait a moment to show completion, then go back
      await Future.delayed(const Duration(seconds: 1));
      
      if (mounted && !_cancelRequested) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (_cancelRequested && e is DioError && e.type == DioErrorType.cancel) {
        LoggerService.info('Download canceled by user');
        // Delete any partially downloaded file
        if (_downloadedFilePath.isNotEmpty) {
          try {
            File(_downloadedFilePath).deleteSync();
          } catch (deleteError) {
            LoggerService.warning('Failed to delete partial file: $deleteError');
          }
        }
      } else {
        LoggerService.error('Error during download: $e');
        setState(() {
          _status = 'Download failed: ${e.toString()}';
        });
        
        // Show error dialog
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Download Failed'),
              content: Text('Failed to download the file: ${e.toString()}'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context, false); // Close download screen
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
      }
    }
  }

  String _extractFileName(String url) {
    try {
      final uri = Uri.parse(url);
      final segments = uri.pathSegments;
      if (segments.isNotEmpty) {
        final fileName = segments.last;
        if (fileName.contains('.')) {
          return fileName;
        }
      }
    } catch (e) {
      LoggerService.warning('Failed to extract filename from URL: $e');
    }
    
    // Fallback to a generic name with timestamp
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'module_content_$timestamp';
  }

  @override
  void dispose() {
    _cancelRequested = true;
    super.dispose();
  }
}
