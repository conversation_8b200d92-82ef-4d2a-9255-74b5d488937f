import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/course_model.dart';
import '../../providers/course_provider.dart';
import '../../widgets/universal_module_card.dart';
import '../../services/logger_service.dart';
import '../../services/content_handler_service.dart';
import '../../theme/app_theme.dart';
import '../../localization/app_localizations_extension.dart';
import '../../config/app_config.dart';

/// Universal course detail page that handles both regular and enrolled courses
class UniversalCourseDetailPage extends StatefulWidget {
  final Course course;
  final bool isEnrolled;

  const UniversalCourseDetailPage({
    super.key,
    required this.course,
    this.isEnrolled = false,
  });

  // Factory constructors for clarity
  factory UniversalCourseDetailPage.regular({
    required Course course,
  }) {
    return UniversalCourseDetailPage(
      course: course,
      isEnrolled: false,
    );
  }

  factory UniversalCourseDetailPage.enrolled({
    required Course course,
  }) {
    return UniversalCourseDetailPage(
      course: course,
      isEnrolled: true,
    );
  }

  @override
  State<UniversalCourseDetailPage> createState() => _UniversalCourseDetailPageState();
}

class _UniversalCourseDetailPageState extends State<UniversalCourseDetailPage> {
  EnrolledCourse? _enrolledCourseData;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.isEnrolled) {
      _loadEnrolledCourseData();
    }
  }

  Future<void> _loadEnrolledCourseData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final courseProvider = Provider.of<CourseProvider>(context, listen: false);
      final enrolledCourses = courseProvider.enrolledCourses;
      
      _enrolledCourseData = enrolledCourses.firstWhere(
        (enrolledCourse) => enrolledCourse.id == widget.course.id,
        orElse: () => throw Exception('Enrolled course not found'),
      ) as EnrolledCourse?;
    } catch (e) {
      LoggerService.error('Error loading enrolled course data', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading course data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Unified watch module method with enhanced content type detection
  Future<void> _watchModule(BuildContext context, Module module) async {
    try {
      LoggerService.info('Starting module: ${module.name}');

      // Use unified content type detection
      final hasScormData = module.scormDataPath.isNotEmpty;
      final contentType = module.contentType?.toUpperCase() ?? '';
      final isScorm = hasScormData || contentType == 'SCORM';

      if (isScorm) {
        // SCORM content - use SCORM player URL
        final String url = '${AppConfig.apiBaseUrl}${AppConfig.apiScormPlayer}/${widget.course.slug}/${module.moduleSlug}';
        LoggerService.info('Opening SCORM module: $url');

        if (context.mounted) {
          await ContentHandlerService.openScormModule(context, url);
        }
      } else {
        // PDF/Audio/Video content - use unified media handler
        final String url = '${AppConfig.apiBaseUrl}${module.downloadLink}';
        LoggerService.info('Opening media module: $url');

        if (context.mounted) {
          await ContentHandlerService.openMediaModuleUnified(
            context, 
            url, 
            module.name,
            moduleId: module.id, // Enable progress tracking for regular modules
            contentType: contentType,
          );
        }
      }
    } catch (e) {
      LoggerService.error('Error watching module', e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.course.title),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course header
                  _buildCourseHeader(),
                  const SizedBox(height: 24),
                  
                  // Course description
                  if (widget.course.description.isNotEmpty) ...[
                    _buildCourseDescription(),
                    const SizedBox(height: 24),
                  ],
                  
                  // Modules section
                  _buildModulesSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildCourseHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.course.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (widget.course.summary?.isNotEmpty == true) ...[
              Text(
                widget.course.summary!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Course stats
            Row(
              children: [
                if (widget.isEnrolled && _enrolledCourseData != null) ...[
                  _buildStatChip(
                    icon: Icons.check_circle,
                    label: _enrolledCourseData!.completionStatus >= 1.0 
                        ? 'Completed' 
                        : '${(_enrolledCourseData!.completionStatus * 100).toInt()}% Complete',
                    color: _enrolledCourseData!.completionStatus >= 1.0 ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                ],
                _buildStatChip(
                  icon: Icons.play_lesson,
                  label: '${_getModuleCount()} modules',
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCourseDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('courses.description'),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.course.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModulesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('courses.modules'),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        if (widget.isEnrolled && _enrolledCourseData != null)
          _buildEnrolledModules()
        else
          _buildRegularModules(),
      ],
    );
  }

  Widget _buildEnrolledModules() {
    final modules = _enrolledCourseData!.modules;
    
    if (modules.isEmpty) {
      return const Center(
        child: Text('No modules available'),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: modules.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final module = modules[index];
        
        return UniversalModuleCard.enrolled(
          enrolledModule: module,
          courseSlug: widget.course.slug,
        );
      },
    );
  }

  Widget _buildRegularModules() {
    final modules = widget.course.modules;
    
    if (modules.isEmpty) {
      return const Center(
        child: Text('No modules available'),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: modules.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final module = modules[index];
        
        return UniversalModuleCard.regular(
          module: module,
          courseSlug: widget.course.slug,
          onWatch: () => _watchModule(context, module),
        );
      },
    );
  }

  int _getModuleCount() {
    if (widget.isEnrolled && _enrolledCourseData != null) {
      return _enrolledCourseData!.modules.length;
    }
    return widget.course.modules.length;
  }
}
