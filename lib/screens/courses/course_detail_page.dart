import 'dart:ffi';
import 'package:ZABAI/theme/app_theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../localization/app_localizations_extension.dart';
import '../../config/app_config.dart';
import '../../models/course_model.dart';
import '../../models/library_item_model.dart';
import '../../providers/course_provider.dart';
import '../../theme/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../services/course_service.dart';
import '../../services/logger_service.dart';
import '../../services/content_handler_service.dart';
import '../../providers/library_provider.dart';
import '../../widgets/module_card.dart';
import '../../widgets/universal_library_card.dart';
import '../library/pdf_viewer_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class CourseDetailPage extends StatefulWidget {
  final Course course;

  const CourseDetailPage({Key? key, required this.course}) : super(key: key);

  @override
  State<CourseDetailPage> createState() => _CourseDetailPageState();
}

class _CourseDetailPageState extends State<CourseDetailPage> {
  List<LibraryItem> _relatedLibraryItems = [];
  bool _isLoadingLibraryItems = false;
  bool _hasError = false;
  bool _isEnrolled = false;
  bool _isEnrolling = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      _isEnrolled = Provider.of<CourseProvider>(context, listen: false).isCourseEnrolled(widget.course.id);
    });

    _fetchRelatedLibraryContent();
  }

  // Fetch related library content for this course
  Future<void> _fetchRelatedLibraryContent() async {
    try {
      setState(() {
        _isLoadingLibraryItems = true;
        _hasError = false;
      });

      final items = await CourseService.fetchRelatedLibraryContent(widget.course.slug);

      if (mounted) {
        setState(() {
          _relatedLibraryItems = items;
          _isLoadingLibraryItems = false;
        });
      }
    } catch (e) {
      LoggerService.error('Error fetching related library content', e);
      if (mounted) {
        setState(() {
          _isLoadingLibraryItems = false;
          _hasError = true;
        });
      }
    }
  }

  // Method to handle watching a module online
  Future<void> _watchModule(BuildContext context, Module module) async {
    try {
      LoggerService.info('Starting module: ${module.name}');

      if (module.scormDataPath.isNotEmpty) {
        // SCORM content - use SCORM player URL
        final String url = '${AppConfig.apiBaseUrl}${AppConfig.apiScormPlayer}/${widget.course.slug}/${module.moduleSlug}';
        LoggerService.info('Opening SCORM module: $url');

        if (context.mounted) {
          await ContentHandlerService.openScormModule(context, url);
        }
      } else {
        // PDF/Audio/Video content - use direct download link
        final String url = '${AppConfig.apiBaseUrl}${module.downloadLink}';
        LoggerService.info('Opening media module: $url');

        if (context.mounted) {
          await ContentHandlerService.openMediaModule(context, url, module.name);
        }
      }
    } catch (e) {
      LoggerService.error('Error watching module', e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Method to handle downloading a module for offline viewing
  void _downloadModule(BuildContext context, Module module) {
    final String downloadLink = module.downloadLink;
    if (downloadLink.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('courses.downloading', args: {'name': module.name})),
        ),
      );
      // TODO: module download functionality
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('errors.something_went_wrong')),
        ),
      );
    }
  }

  // Handle downloading a library item
  void _downloadLibraryItem(LibraryItem item) {
    LoggerService.info('Downloading library item: ${item.contentTitle}');

    // Check if this is an external link item
    if (item.isExternalLink) {
      _openLibraryItem(item);
      return;
    }

    // If library_downloadable is false, open content directly in WebView without downloading
    if (!AppConfig.libraryDownloadable && item.contentFile.isNotEmpty) {
      _openLibraryItem(item);
      return;
    }

    // Get the LibraryProvider
    final libraryProvider = Provider.of<LibraryProvider>(context, listen: false);

    // Show download progress dialog
    _showDownloadProgressDialog(item);

    // Start the download using the LibraryProvider
    libraryProvider.downloadItem(item).then((success) {
      if (success && mounted) {
        // Close the progress dialog if it's still showing
        Navigator.of(context).pop();

        // Refresh the list to show the downloaded status
        _fetchRelatedLibraryContent();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('library.download_complete', args: {'name': item.contentTitle})),
            backgroundColor: Colors.green,
          ),
        );
      }
    }).catchError((error) {
      if (mounted) {
        // Close the progress dialog if it's still showing
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('errors.download_failed', args: {'name': item.contentTitle})),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  // Show download progress dialog
  void _showDownloadProgressDialog(LibraryItem item) {
    // Get content type color for styling
    Color contentTypeColor;
    switch (item.contentType.toLowerCase()) {
      case 'pdf':
        contentTypeColor = Colors.red.shade700;
        break;
      case 'video':
      case 'mp4':
        contentTypeColor = Colors.blue.shade700;
        break;
      case 'audio':
      case 'mp3':
        contentTypeColor = Colors.purple.shade700;
        break;
      case 'link':
        contentTypeColor = Colors.green.shade700;
        break;
      default:
        contentTypeColor = Theme.of(context).colorScheme.primary;
    }

    // Get content type icon
    IconData iconData;
    switch (item.contentType.toLowerCase()) {
      case 'pdf':
        iconData = Icons.picture_as_pdf;
        break;
      case 'video':
      case 'mp4':
        iconData = Icons.video_library;
        break;
      case 'audio':
      case 'mp3':
        iconData = Icons.audiotrack;
        break;
      case 'link':
        iconData = Icons.link;
        break;
      default:
        iconData = Icons.insert_drive_file;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // File icon
                  Container(
                    width: 60,
                    height: 60,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: contentTypeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Icon(
                          iconData,
                          size: 30,
                          color: contentTypeColor.withOpacity(0.5),
                        ),
                        Consumer<LibraryProvider>(
                          builder: (context, libraryProvider, child) {
                            // Get the current download progress
                            final progress = libraryProvider.getDownloadProgressForItem(item.id);
                            final isDownloading = libraryProvider.isItemDownloading(item.id);

                            return SizedBox(
                              width: 40,
                              height: 40,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                // Use determinate progress when available
                                value: isDownloading && progress > 0 ? progress : null,
                                valueColor: AlwaysStoppedAnimation<Color>(contentTypeColor),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // File title
                  Text(
                    item.contentTitle,
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // Progress message
                  Text(
                    context.tr('library.downloading'),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                // Cancel button
                Consumer<LibraryProvider>(
                  builder: (context, libraryProvider, child) {
                    return TextButton.icon(
                      onPressed: () {
                        // Cancel the download using the LibraryProvider
                        libraryProvider.cancelDownload(item);
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.cancel, size: 16),
                      label: Text(context.tr('common.cancel')),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    );
                  },
                ),
              ],
              actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            );
          },
        );
      },
    );
  }

  // Handle opening a library item
  void _openLibraryItem(LibraryItem item) {
    LoggerService.info('Opening library item: ${item.contentTitle}');

    // Use ContentHandlerService to open the library item
    ContentHandlerService.openLibraryItem(
      context,
      item,
      showErrorSnackbar: true,
    ).then((success) {
      // If the item is not available for offline viewing and doesn't have a content link,
      // try to download it first
      if (!success && !item.isDownloaded && item.contentLink.isEmpty) {
        _downloadLibraryItem(item);
      }
    });
  }

  // Build the related library content section
  Widget _buildRelatedLibraryContent() {
    if (_isLoadingLibraryItems) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                context.tr('errors.something_went_wrong'),
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              TextButton(
                onPressed: _fetchRelatedLibraryContent,
                child: Text(context.tr('common.retry')),
              ),
            ],
          ),
        ),
      );
    }

    if (_relatedLibraryItems.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          context.tr('courses.no_related_content'),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                context.tr('courses.related_library_content'),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                softWrap: true,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '${_relatedLibraryItems.length} ${context.tr('library.items').toLowerCase()}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 220, // Fixed height for the horizontal list
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _relatedLibraryItems.length,
            itemBuilder: (context, index) {
              final item = _relatedLibraryItems[index];
              return Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: SizedBox(
                  width: 140, // Fixed width for the card
                  child: UniversalLibraryCard(
                    item: item,
                    isHorizontal: false,
                    onDownload: _downloadLibraryItem,
                    onOpen: _openLibraryItem,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {

    double _isCompleted = Provider.of<CourseProvider>(context).getCompletionStatus(widget.course.id);
    final certId = Provider.of<CourseProvider>(context).getCertificateId(widget.course.id);
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with back button overlaying the banner
          SliverAppBar(
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            // flexibleSpace: FlexibleSpaceBar(
            //   background: CachedNetworkImage(
            //     imageUrl: '${AppConfig.apiBaseUrl}${widget.course.banner}',
            //     fit: BoxFit.cover,
            //     width: double.infinity,
            //     placeholder: (context, url) => Container(
            //       color: Theme.of(context).colorScheme.surfaceVariant,
            //       child: const Center(child: CircularProgressIndicator()),
            //     ),
            //     errorWidget: (context, url, error) => Container(
            //       color: Theme.of(context).colorScheme.surfaceVariant,
            //       child: const Icon(Icons.error),
            //     ),
            //   ),
            // ),
            expandedHeight: 200,
          ),

          // Course Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course Title and Description
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Course Title
                      Text(
                        widget.course.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Course Description
                      Text(
                        widget.course.description,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Modules Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr('courses.modules'),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      _isCompleted == 100 ?
                      ElevatedButton.icon(
                        onPressed: () {
                          openInBrowser("${AppConfig.apiBaseUrl}${AppConfig.certificateDownloadLink}/$certId");
                        },
                        icon: Icon(Icons.download, size: 16),
                        label: Text(context.tr("courses.certificate")),
                        style: Theme.of(context).smallButton, // 👈 Use your extension here
                      ):
                      Text(
                        '${widget.course.modules.length} ${context.tr('courses.modules').toLowerCase()}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 0.5),
                  // Module List
                  _isEnrolling
                      ? ListView.builder(
                    itemCount: 3,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (_, __) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Container(
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  )
                      : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.course.modules.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final module = widget.course.modules[index];
                      final isLocked = Provider.of<CourseProvider>(context).isModuleLocked(module.id);
                      final isCompleted = Provider.of<CourseProvider>(context).isModuleCompleted(module.id);

                      IconData iconData;
                      Color? iconColor;

                      if (isLocked) {
                        iconData = Icons.lock;
                        iconColor = Colors.red;
                      } else if (isCompleted) {
                        iconData = Icons.check_circle;
                        iconColor = Colors.green;
                      } else {
                        iconData = Icons.visibility;
                        iconColor = Colors.blue;
                      }

                      return ModuleCard(
                        courseSlug: widget.course.slug,
                        module: module,
                        onWatch: _isEnrolled && !isLocked ? () => _watchModule(context, module) : () {},
                        iconData: iconData,
                        iconColor: iconColor,
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Related Library Content Section
                  _buildRelatedLibraryContent(),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: !_isEnrolled
          ? FloatingActionButton.extended(
        icon: const Icon(Icons.add),
        label: const Text('Enroll Now'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        onPressed: _isEnrolling
            ? null
            : () async {
          setState(() => _isEnrolling = true);

          // Run enrollment and 3-second delay in parallel
          final results = await Future.wait([
            CourseService.enrollToCourse(context, widget.course),
            Future.delayed(const Duration(seconds: 3)),
          ]);

          final enrollSuccess = results[0] == true;

          if (enrollSuccess) {
            _showEnrollmentSuccessDialog();
            setState(() {
              _isEnrolled = true;
            });
          } else {
            _showSnackBar("Couldn't enroll you :(", isError: true);
          }

          setState(() => _isEnrolling = false);
        },
      )
          : null,

    );
  }

  // Show a snackbar message
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: AppTheme.fontSizeRegular,
            color: Theme.of(context).colorScheme.onError,
          ),
        ),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : AppTheme.successColor,
        duration: const Duration(seconds: 5), // Increased to 5 seconds
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
      ),
    );
  }

  Future<void> openInBrowser(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication, // Opens in default browser
      );
    } else {
      throw 'Could not launch $url';
    }
  }

  void _showEnrollmentSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 64),
              const SizedBox(height: 16),
              Text(
                context.tr("courses.enroll_success"),
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              child: Text(context.tr("common.ok")),
              onPressed: () => Navigator.of(context).pop(),
            )
          ],
        );
      },
    );
  }


}
