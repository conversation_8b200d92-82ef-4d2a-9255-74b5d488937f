import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/course_model.dart';
import '../../providers/course_provider.dart';
import '../../widgets/enrolled_module_card.dart';
import '../../services/logger_service.dart';
import '../../theme/app_theme.dart';
import '../../localization/app_localizations_extension.dart';
import '../../config/app_config.dart';

class EnrolledCourseDetailPage extends StatefulWidget {
  final Course course;

  const EnrolledCourseDetailPage({
    super.key,
    required this.course,
  });

  @override
  State<EnrolledCourseDetailPage> createState() => _EnrolledCourseDetailPageState();
}

class _EnrolledCourseDetailPageState extends State<EnrolledCourseDetailPage> {
  EnrolledCourse? _enrolledCourseData;

  @override
  void initState() {
    super.initState();
    _loadEnrolledCourseData();
  }

  /// Load the enrolled course data with modules
  void _loadEnrolledCourseData() {
    final courseProvider = Provider.of<CourseProvider>(context, listen: false);
    try {
      _enrolledCourseData = courseProvider.enrolledCoursesData.firstWhere(
        (enrolledCourse) => enrolledCourse.id == widget.course.id,
      );
    } catch (e) {
      LoggerService.error('Error finding enrolled course data for course ${widget.course.id}', e);
    }
  }

  /// Open URL in browser
  Future<void> openInBrowser(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with back button overlaying the banner
          SliverAppBar(
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),

          // Course content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Course banner
                Container(
                  height: 250,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: NetworkImage(widget.course.banner),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                // Course details
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Course title
                      Text(
                        widget.course.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Progress indicator
                      Consumer<CourseProvider>(
                        builder: (context, courseProvider, child) {
                          final completionStatus = courseProvider.getCompletionStatus(widget.course.id);
                          final certId = courseProvider.getCertificateId(widget.course.id);

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    context.tr('courses.progress'),
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    '${completionStatus.toInt()}%',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              LinearProgressIndicator(
                                value: completionStatus / 100,
                                backgroundColor: Colors.grey.shade300,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              
                              // Certificate download button
                              if (completionStatus == 100 && certId != null) ...[
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    openInBrowser("${AppConfig.apiBaseUrl}${AppConfig.certificateDownloadLink}/$certId");
                                  },
                                  icon: const Icon(Icons.download, size: 16),
                                  label: Text(context.tr("courses.certificate")),
                                  style: Theme.of(context).elevatedButtonTheme.style,
                                ),
                              ],
                            ],
                          );
                        },
                      ),

                      const SizedBox(height: 24),

                      // Course description
                      if (widget.course.description.isNotEmpty) ...[
                        Text(
                          context.tr('courses.description'),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.course.description,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Modules section
                      _buildModulesSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the modules section
  Widget _buildModulesSection() {
    if (_enrolledCourseData == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('courses.modules'),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Center(
            child: Text('No module data available'),
          ),
        ],
      );
    }

    final modules = _enrolledCourseData!.modules;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Modules header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr('courses.modules'),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${modules.length} ${context.tr('courses.modules').toLowerCase()}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),

        // Modules list
        if (modules.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(24.0),
              child: Text('No modules available'),
            ),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: modules.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final module = modules[index];

              return EnrolledModuleCard(
                module: module,
                courseSlug: widget.course.slug,
              );
            },
          ),
      ],
    );
  }
}
