import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../screens/courses/course_detail_page.dart';
import '../../screens/courses/enrolled_course_detail_page.dart';
import '../../theme/app_theme.dart';
import '../../widgets/universal_course_card.dart';
import '../../localization/app_localizations_extension.dart';
import '../../utils/responsive_utils.dart';
import '../../providers/course_provider.dart';

class EnrolledCoursesPage extends StatefulWidget {
  @override
  _EnrolledCoursesPageState createState() => _EnrolledCoursesPageState();
}

class _EnrolledCoursesPageState extends State<EnrolledCoursesPage> with AutomaticKeepAliveClientMixin {
  // Override wantKeepAlive to keep this page alive when not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Make sure the course provider is initialized and fetch enrolled courses
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final courseProvider = Provider.of<CourseProvider>(context, listen: false);
      if (courseProvider.courses.isEmpty) {
        courseProvider.init();
      }
      // Fetch enrolled courses with context to ensure user ID is available
      courseProvider.fetchEnrolledCourses(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to maintain the AutomaticKeepAliveClientMixin
    super.build(context);

    return Consumer<CourseProvider>(
      builder: (context, courseProvider, child) {
        if (courseProvider.isLoadingEnrolled) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(24.0),
              child: CircularProgressIndicator(),
            ),
          );
        } else if (courseProvider.enrolledError.isNotEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    courseProvider.enrolledError,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      courseProvider.fetchEnrolledCourses(
                        forceRefresh: true,
                        context: context,
                      );
                    },
                    child: Text(context.tr('errors.try_again')),
                  ),
                ],
              ),
            ),
          );
        } else if (courseProvider.enrolledCourses.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Text(context.tr('courses.no_enrolled_courses')),
            ),
          );
        }

        final courses = courseProvider.enrolledCourses;

        return RefreshIndicator(
          onRefresh: () async {
            await courseProvider.fetchEnrolledCourses(
              forceRefresh: true,
              context: context,
            );
          },
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Determine the number of columns based on screen width
              final crossAxisCount = ResponsiveUtils.getResponsiveGridCount(
                context: context,
                // Let the utility use its default values (2 for mobile, 3 for tablet, 4 for desktop)
              );

              // Adjust spacing based on screen size
              final spacing = ResponsiveUtils.getValueForDeviceType(
                context: context,
                mobile: 16.0,
                tablet: 20.0,
                desktop: 24.0,
              );

            // Use the standard aspect ratio from AppTheme
            const childAspectRatio = AppTheme.cardAspectRatio;

            // Determine how many courses to show based on screen size
            final itemCount = ResponsiveUtils.getValueForDeviceType<int>(
              context: context,
              mobile: 4,
              tablet: 4,
              desktop: 6,
            );

            return GridView.builder(
              shrinkWrap: true, // Ensures the grid doesn't take more space than needed
              physics: const NeverScrollableScrollPhysics(), // Disables scrolling for this grid (let parent scroll)
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: spacing,
                mainAxisSpacing: spacing,
                childAspectRatio: childAspectRatio,
              ),
              itemCount: courses.length,
              itemBuilder: (context, index) {
                final course = courses[index];
                return UniversalCourseCard(
                  course: course,
                  isHorizontal: false,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EnrolledCourseDetailPage(course: course),
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
        );
      },
    );
  }
}
