import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../providers/user_provider.dart';
import '../../services/logger_service.dart';
import '../../services/module_progress_service.dart';
import '../../localization/app_localizations_extension.dart';
import '../../theme/app_theme.dart';

class PDFViewerScreen extends StatefulWidget {
  final String pdfUrl;
  final String title;
  final int? moduleId; // Optional module ID for tracking completion

  const PDFViewerScreen({
    super.key,
    required this.pdfUrl,
    required this.title,
    this.moduleId,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  late PdfViewerController controller;
  bool isLoading = true;
  bool hasError = false;
  String? errorMessage;
  int currentPageNumber = 1;
  int totalPages = 0;
  bool _hasMarkedAsAccessed = false;
  bool _hasMarkedAsCompleted = false;
  Set<int> _viewedPages = {};

  @override
  void initState() {
    super.initState();
    controller = PdfViewerController();

    // Check if it's a local file path or network URL
    final isLocalFile = widget.pdfUrl.startsWith('/') || widget.pdfUrl.startsWith('file://');
    LoggerService.info('Loading PDF from ${isLocalFile ? 'local file' : 'URL'}: ${widget.pdfUrl}');
    _markAsAccessed();
  }

  /// Check if the PDF source is a local file
  bool get _isLocalFile => widget.pdfUrl.startsWith('/') || widget.pdfUrl.startsWith('file://');

  /// Mark module as accessed when PDF is opened
  Future<void> _markAsAccessed() async {
    if (widget.moduleId != null && !_hasMarkedAsAccessed) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;

        if (user != null) {
          await ModuleProgressService.markAsAccessed(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'PDF',
          );
          _hasMarkedAsAccessed = true;
          LoggerService.debug('Marked PDF module ${widget.moduleId} as accessed');
        }
      } catch (e) {
        LoggerService.error('Error marking PDF module as accessed', e);
      }
    }
  }

  /// Check if PDF should be marked as completed
  void _checkCompletion() {
    if (widget.moduleId != null && !_hasMarkedAsCompleted && totalPages > 0) {
      // Mark as completed if user has viewed at least 80% of pages
      final viewedPercentage = _viewedPages.length / totalPages;
      if (viewedPercentage >= 0.8) {
        _markAsCompleted();
      }
    }
  }

  /// Mark module as completed
  Future<void> _markAsCompleted() async {
    if (widget.moduleId != null && !_hasMarkedAsCompleted) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;

        if (user != null) {
          await ModuleProgressService.markAsCompleted(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'PDF',
            progressData: {
              'total_pages': totalPages,
              'viewed_pages': _viewedPages.length,
              'completion_percentage': _viewedPages.length / totalPages,
            },
          );
          _hasMarkedAsCompleted = true;
          LoggerService.debug('Marked PDF module ${widget.moduleId} as completed');

          // Show completion message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Module completed!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } catch (e) {
        LoggerService.error('Error marking PDF module as completed', e);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (totalPages > 0)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '$currentPageNumber / $totalPages',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('library.pdf_load_error'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                errorMessage ?? context.tr('library.unknown_error'),
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    hasError = false;
                    isLoading = true;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(context.tr('common.retry')),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        _isLocalFile
            ? SfPdfViewer.file(
                File(widget.pdfUrl),
                controller: controller,
                onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                  setState(() {
                    totalPages = details.document.pages.count;
                    isLoading = false;
                  });
                  LoggerService.info(
                      'PDF loaded successfully from local file. Total pages: $totalPages');
                },
                onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                  setState(() {
                    isLoading = false;
                    hasError = true;
                    errorMessage = details.error;
                  });
                  LoggerService.error('PDF load failed from local file: ${details.error}', null);
                },
                onPageChanged: (PdfPageChangedDetails details) {
                  setState(() {
                    currentPageNumber = details.newPageNumber;
                    _viewedPages.add(details.newPageNumber);
                  });

                  // Check if module should be marked as completed
                  _checkCompletion();
                },
              )
            : SfPdfViewer.network(
                widget.pdfUrl,
                controller: controller,
                onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                  setState(() {
                    totalPages = details.document.pages.count;
                    isLoading = false;
                  });
                  LoggerService.info(
                      'PDF loaded successfully from network. Total pages: $totalPages');
                },
                onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                  setState(() {
                    isLoading = false;
                    hasError = true;
                    errorMessage = details.error;
                  });
                  LoggerService.error('PDF load failed from network: ${details.error}', null);
                },
                onPageChanged: (PdfPageChangedDetails details) {
                  setState(() {
                    currentPageNumber = details.newPageNumber;
                    _viewedPages.add(details.newPageNumber);
                  });

                  // Check if module should be marked as completed
                  _checkCompletion();
                },
              ),
        if (isLoading)
          Container(
            color: AppTheme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('library.loading_pdf'),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
