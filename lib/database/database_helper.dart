import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../models/course_model.dart';
import '../models/user_model.dart';
import '../models/library_item_model.dart';
import '../services/logger_service.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  // Database name and version
  static const String _databaseName = "score_app.db";
  static const int _databaseVersion = 8;

  // Table names
  static const String tableCourses = 'courses';
  static const String tableModules = 'modules';
  static const String tableTags = 'tags';
  static const String tableCoursesTags = 'courses_tags';
  static const String tableUsers = 'users';
  static const String tableLibraryItems = 'library_items';
  static const String tableEnrolledCourses = 'enrolled_courses';
  static const String tableEnrolledModules = 'enrolled_modules';
  static const String tableScormProgress = 'scorm_progress';
  static const String tableModuleProgress = 'module_progress';

  // Common column names
  static const String columnId = 'id';

  // Courses table columns
  static const String columnBanner = 'banner';
  static const String columnTitle = 'title';
  static const String columnSlug = 'slug';
  static const String columnDescription = 'description';
  static const String columnLanguage = 'language';
  static const String columnCategory = 'category';
  static const String columnCreatedAt = 'created_at';
  static const String columnUpdatedAt = 'updated_at';
  static const String columnCourseSummary = 'course_summary';
  static const String columnStartDate = 'start_date';
  static const String columnEndDate = 'end_date';

  // Modules table columns
  static const String columnCourseId = 'course_id';
  static const String columnCourseName = 'course_name';
  static const String columnName = 'name';
  static const String columnModuleSlug = 'module_slug';
  static const String columnDownloadLink = 'download_link';
  static const String columnScormDataPath = 'scorm_data_path';
  static const String columnIsDownloaded = 'is_downloaded';
  static const String columnLocalPath = 'local_path';
  static const String columnModuleSummary = 'module_summary';
  static const String columnAccessible = 'accessible';
  static const String columnDependentModules = 'dependent_modules';
  static const String columnModuleContentType = 'module_content_type';

  // Tags table columns
  static const String columnTagName = 'tag_name';

  // User table columns
  static const String columnUsername = 'username';
  static const String columnFirstName = 'first_name';
  static const String columnLastName = 'last_name';
  static const String columnEmail = 'email';
  static const String columnPhone = 'phone';
  static const String columnAvatar = 'avatar';
  static const String columnCoverPhoto = 'cover_photo';
  static const String columnSummary = 'summary';
  static const String columnToken = 'token';
  static const String columnExperiences = 'experiences';

  // Library items table columns
  static const String columnAuthor = 'author';
  static const String columnContentType = 'content_type';
  static const String columnContentTitle = 'content_title';
  static const String columnContentLink = 'content_link';
  static const String columnContentSummary = 'content_summary';
  static const String columnContentFile = 'content_file';
  static const String columnContentThumbnail = 'content_thumbnail';
  static const String columnLocalFilePath = 'local_file_path';
  static const String columnContentCategory = 'category';

  // Enrolled courses table columns
  static const String columnUserId = 'user_id';
  static const String columnCompletionStatus = 'completion_status';
  static const String columnCertificate = 'certificate';
  static const String columnEnrollmentDate = 'enrollment_date';

  // Enrolled modules table columns
  static const String columnEnrolledCourseId = 'enrolled_course_id';
  static const String columnModuleId = 'module_id';
  static const String columnModuleCompletionStatus = 'module_completion_status';
  static const String columnModuleLock = 'module_lock';
  static const String columnModuleState = 'module_state';
  static const String columnModuleDependentList = 'module_dependent_list';
  static const String columnModuleContentType = 'module_content_type';
  static const String columnModuleDownloadLink = 'module_download_link';
  static const String columnModuleScormDataPath = 'module_scorm_data_path';

  // SCORM progress table columns
  static const String columnScormData = 'scorm_data';
  static const String columnLastAccessed = 'last_accessed';
  static const String columnSyncStatus = 'sync_status';
  static const String columnSyncAttempts = 'sync_attempts';

  // Module progress table columns (for non-SCORM content)
  static const String columnContentType = 'content_type';
  static const String columnCompletionStatus = 'completion_status';
  static const String columnProgressData = 'progress_data';

  // Singleton constructor
  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize the database
  Future<Database> _initDatabase() async {
    try {
      // Use application documents directory (works on both iOS and Android)
      Directory storageDirectory = await getApplicationDocumentsDirectory();

      // Create a SCORE directory in the application documents directory
      final scoreDir = Directory('${storageDirectory.path}/SCORE');
      if (!await scoreDir.exists()) {
        await scoreDir.create(recursive: true);
      }
      storageDirectory = scoreDir;

      String path = join(storageDirectory.path, _databaseName);
      LoggerService.debug('Database path: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      LoggerService.error('Error initializing database', e);
      rethrow;
    }
  }

  // Create database tables
  Future<void> _onCreate(Database db, int version) async {
    try {
      LoggerService.debug('Creating database tables');

      // Create courses table
      await db.execute('''
        CREATE TABLE $tableCourses (
          $columnId INTEGER PRIMARY KEY,
          $columnBanner TEXT NOT NULL,
          $columnTitle TEXT NOT NULL,
          $columnSlug TEXT NOT NULL,
          $columnDescription TEXT,
          $columnLanguage TEXT,
          $columnCategory TEXT,
          $columnCreatedAt TEXT,
          $columnUpdatedAt TEXT,
          $columnCourseSummary TEXT,
          $columnStartDate TEXT,
          $columnEndDate TEXT
        )
      ''');

      // Create modules table
      await db.execute('''
        CREATE TABLE $tableModules (
          $columnId INTEGER PRIMARY KEY,
          $columnCourseId INTEGER NOT NULL,
          $columnCourseName TEXT,
          $columnDescription TEXT,
          $columnName TEXT NOT NULL,
          $columnModuleSlug TEXT NOT NULL,
          $columnDownloadLink TEXT,
          $columnScormDataPath TEXT,
          $columnIsDownloaded INTEGER DEFAULT 0,
          $columnLocalPath TEXT,
          $columnModuleSummary TEXT,
          $columnAccessible INTEGER DEFAULT 1,
          $columnDependentModules TEXT,
          $columnModuleContentType TEXT,
          FOREIGN KEY ($columnCourseId) REFERENCES $tableCourses ($columnId) ON DELETE CASCADE
        )
      ''');

      // Create tags table
      await db.execute('''
        CREATE TABLE $tableTags (
          $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
          $columnTagName TEXT UNIQUE NOT NULL
        )
      ''');

      // Create courses_tags junction table
      await db.execute('''
        CREATE TABLE $tableCoursesTags (
          $columnCourseId INTEGER,
          $columnId INTEGER,
          PRIMARY KEY ($columnCourseId, $columnId),
          FOREIGN KEY ($columnCourseId) REFERENCES $tableCourses ($columnId) ON DELETE CASCADE,
          FOREIGN KEY ($columnId) REFERENCES $tableTags ($columnId) ON DELETE CASCADE
        )
      ''');

      // Create users table
      await db.execute('''
        CREATE TABLE $tableUsers (
          $columnId INTEGER PRIMARY KEY,
          $columnUsername TEXT NOT NULL UNIQUE,
          $columnFirstName TEXT NOT NULL,
          $columnLastName TEXT NOT NULL,
          $columnEmail TEXT,
          $columnPhone TEXT,
          $columnAvatar TEXT,
          $columnCoverPhoto TEXT,
          $columnSummary TEXT,
          $columnToken TEXT NOT NULL,
          $columnCreatedAt TEXT NOT NULL,
          $columnUpdatedAt TEXT NOT NULL,
          $columnExperiences TEXT DEFAULT '{}'
        )
      ''');

      // Create library items table
      await db.execute('''
        CREATE TABLE $tableLibraryItems (
          $columnId INTEGER PRIMARY KEY,
          $columnAuthor TEXT,
          $columnCreatedAt TEXT,
          $columnContentType TEXT,
          $columnContentTitle TEXT,
          $columnContentLink TEXT,
          $columnContentSummary TEXT,
          $columnContentFile TEXT,
          $columnContentThumbnail TEXT,
          $columnIsDownloaded INTEGER DEFAULT 0,
          $columnLocalFilePath TEXT,
          $columnContentCategory TEXT
        )
      ''');

      // Create enrolled courses table
      await db.execute('''
        CREATE TABLE $tableEnrolledCourses (
          $columnId INTEGER PRIMARY KEY,
          $columnUserId INTEGER NOT NULL,
          $columnCompletionStatus REAL DEFAULT 0.0,
          $columnCertificate TEXT,
          $columnEnrollmentDate TEXT NOT NULL,
          $columnCreatedAt TEXT NOT NULL,
          $columnUpdatedAt TEXT NOT NULL,
          FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
          FOREIGN KEY ($columnId) REFERENCES $tableCourses ($columnId) ON DELETE CASCADE,
          UNIQUE($columnId, $columnUserId)
        )
      ''');

      // Create enrolled modules table
      await db.execute('''
        CREATE TABLE $tableEnrolledModules (
          $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
          $columnEnrolledCourseId INTEGER NOT NULL,
          $columnModuleId INTEGER NOT NULL,
          $columnModuleCompletionStatus INTEGER DEFAULT 0,
          $columnModuleLock INTEGER DEFAULT 1,
          $columnModuleState TEXT,
          $columnModuleDependentList TEXT,
          $columnModuleContentType TEXT,
          $columnModuleDownloadLink TEXT,
          $columnModuleScormDataPath TEXT,
          $columnCreatedAt TEXT NOT NULL,
          $columnUpdatedAt TEXT NOT NULL,
          FOREIGN KEY ($columnEnrolledCourseId) REFERENCES $tableEnrolledCourses ($columnId) ON DELETE CASCADE,
          FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
          UNIQUE($columnEnrolledCourseId, $columnModuleId)
        )
      ''');

      // Create SCORM progress table
      await db.execute('''
        CREATE TABLE $tableScormProgress (
          $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
          $columnUserId INTEGER NOT NULL,
          $columnModuleId INTEGER NOT NULL,
          $columnScormData TEXT NOT NULL,
          $columnLastAccessed TEXT NOT NULL,
          $columnSyncStatus INTEGER DEFAULT 0,
          $columnSyncAttempts INTEGER DEFAULT 0,
          $columnCreatedAt TEXT NOT NULL,
          $columnUpdatedAt TEXT NOT NULL,
          FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
          FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
          UNIQUE($columnUserId, $columnModuleId)
        )
      ''');

      // Create module progress table (for non-SCORM content)
      await db.execute('''
        CREATE TABLE $tableModuleProgress (
          $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
          $columnUserId INTEGER NOT NULL,
          $columnModuleId INTEGER NOT NULL,
          $columnContentType TEXT NOT NULL,
          $columnCompletionStatus INTEGER DEFAULT 0,
          $columnProgressData TEXT,
          $columnLastAccessed TEXT NOT NULL,
          $columnSyncStatus INTEGER DEFAULT 0,
          $columnSyncAttempts INTEGER DEFAULT 0,
          $columnCreatedAt TEXT NOT NULL,
          $columnUpdatedAt TEXT NOT NULL,
          FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
          FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
          UNIQUE($columnUserId, $columnModuleId)
        )
      ''');

      LoggerService.debug('Database tables created successfully');
    } catch (e) {
      LoggerService.error('Error creating database tables', e);
      rethrow;
    }
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      LoggerService.debug('Upgrading database from version $oldVersion to $newVersion');

      if (oldVersion < 2) {
        // Add users table if upgrading from version 1 to 2
        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableUsers (
            $columnId INTEGER PRIMARY KEY,
            $columnUsername TEXT NOT NULL UNIQUE,
            $columnFirstName TEXT NOT NULL,
            $columnLastName TEXT NOT NULL,
            $columnEmail TEXT,
            $columnPhone TEXT,
            $columnAvatar TEXT,
            $columnCoverPhoto TEXT,
            $columnSummary TEXT,
            $columnToken TEXT NOT NULL,
            $columnCreatedAt TEXT NOT NULL,
            $columnUpdatedAt TEXT NOT NULL,
            $columnExperiences TEXT DEFAULT '{}'
          )
        ''');
        LoggerService.debug('Added users table in database upgrade');

        // If users table already exists, add the missing columns
        try {
          var tableInfo = await db.rawQuery("PRAGMA table_info($tableUsers)");
          bool experiencesExists = tableInfo.any((column) => column['name'] == columnExperiences);
          if (!experiencesExists) {
            await db.execute(
              'ALTER TABLE $tableUsers ADD COLUMN $columnExperiences TEXT DEFAULT \'{}\'');
            LoggerService.debug('Added experiences column to users table');
          }
        } catch (e) {
          LoggerService.error('Error adding missing columns to users table', e);
        }
      }

      if (oldVersion < 3) {
        // Add library items table if upgrading from version 2 to 3
        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableLibraryItems (
            $columnId INTEGER PRIMARY KEY,
            $columnAuthor TEXT,
            $columnCreatedAt TEXT,
            $columnContentType TEXT,
            $columnContentTitle TEXT,
            $columnContentLink TEXT,
            $columnContentSummary TEXT,
            $columnContentFile TEXT,
            $columnContentThumbnail TEXT,
            $columnIsDownloaded INTEGER DEFAULT 0,
            $columnLocalFilePath TEXT
          )
        ''');
        LoggerService.debug('Added library items table in database upgrade');
      }

      if (oldVersion < 4) {
        // Add enrolled courses and enrolled modules tables if upgrading to version 4
        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableEnrolledCourses (
            $columnId INTEGER PRIMARY KEY,
            $columnUserId INTEGER NOT NULL,
            $columnCompletionStatus REAL DEFAULT 0.0,
            $columnCertificate TEXT,
            $columnEnrollmentDate TEXT NOT NULL,
            $columnCreatedAt TEXT NOT NULL,
            $columnUpdatedAt TEXT NOT NULL,
            FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
            FOREIGN KEY ($columnId) REFERENCES $tableCourses ($columnId) ON DELETE CASCADE,
            UNIQUE($columnId, $columnUserId)
          )
        ''');

        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableEnrolledModules (
            $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
            $columnEnrolledCourseId INTEGER NOT NULL,
            $columnModuleId INTEGER NOT NULL,
            $columnModuleCompletionStatus INTEGER DEFAULT 0,
            $columnModuleLock INTEGER DEFAULT 1,
            $columnModuleState TEXT,
            $columnCreatedAt TEXT NOT NULL,
            $columnUpdatedAt TEXT NOT NULL,
            FOREIGN KEY ($columnEnrolledCourseId) REFERENCES $tableEnrolledCourses ($columnId) ON DELETE CASCADE,
            FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
            UNIQUE($columnEnrolledCourseId, $columnModuleId)
          )
        ''');
        LoggerService.debug('Added enrolled courses and enrolled modules tables in database upgrade');
      }

      if (oldVersion < 5) {
        // Add SCORM progress table if upgrading to version 5
        await db.execute('''
          CREATE TABLE IF NOT EXISTS $tableScormProgress (
            $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
            $columnUserId INTEGER NOT NULL,
            $columnModuleId INTEGER NOT NULL,
            $columnScormData TEXT NOT NULL,
            $columnLastAccessed TEXT NOT NULL,
            $columnSyncStatus INTEGER DEFAULT 0,
            $columnSyncAttempts INTEGER DEFAULT 0,
            $columnCreatedAt TEXT NOT NULL,
            $columnUpdatedAt TEXT NOT NULL,
            FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
            FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
            UNIQUE($columnUserId, $columnModuleId)
          )
        ''');
        LoggerService.debug('Added SCORM progress table in database upgrade');
      }

      if (oldVersion < 6) {
        // Add new columns to courses and modules tables for API response changes
        try {
          // Add new columns to courses table
          var coursesTableInfo = await db.rawQuery("PRAGMA table_info($tableCourses)");

          if (!coursesTableInfo.any((column) => column['name'] == columnCourseSummary)) {
            await db.execute('ALTER TABLE $tableCourses ADD COLUMN $columnCourseSummary TEXT');
          }
          if (!coursesTableInfo.any((column) => column['name'] == columnStartDate)) {
            await db.execute('ALTER TABLE $tableCourses ADD COLUMN $columnStartDate TEXT');
          }
          if (!coursesTableInfo.any((column) => column['name'] == columnEndDate)) {
            await db.execute('ALTER TABLE $tableCourses ADD COLUMN $columnEndDate TEXT');
          }

          // Add new columns to modules table
          var modulesTableInfo = await db.rawQuery("PRAGMA table_info($tableModules)");

          if (!modulesTableInfo.any((column) => column['name'] == columnModuleSummary)) {
            await db.execute('ALTER TABLE $tableModules ADD COLUMN $columnModuleSummary TEXT');
          }
          if (!modulesTableInfo.any((column) => column['name'] == columnAccessible)) {
            await db.execute('ALTER TABLE $tableModules ADD COLUMN $columnAccessible INTEGER DEFAULT 1');
          }
          if (!modulesTableInfo.any((column) => column['name'] == columnDependentModules)) {
            await db.execute('ALTER TABLE $tableModules ADD COLUMN $columnDependentModules TEXT');
          }
          if (!modulesTableInfo.any((column) => column['name'] == columnModuleContentType)) {
            await db.execute('ALTER TABLE $tableModules ADD COLUMN $columnModuleContentType TEXT');
          }

          LoggerService.debug('Added new columns for API response changes in database upgrade');
        } catch (e) {
          LoggerService.error('Error adding new columns for API response changes', e);
        }
      }

      if (oldVersion < 7) {
        try {
          // Add new columns to enrolled_modules table for enhanced module data
          var enrolledModulesTableInfo = await db.rawQuery("PRAGMA table_info($tableEnrolledModules)");

          if (!enrolledModulesTableInfo.any((column) => column['name'] == columnModuleDependentList)) {
            await db.execute('ALTER TABLE $tableEnrolledModules ADD COLUMN $columnModuleDependentList TEXT');
          }
          if (!enrolledModulesTableInfo.any((column) => column['name'] == columnModuleContentType)) {
            await db.execute('ALTER TABLE $tableEnrolledModules ADD COLUMN $columnModuleContentType TEXT');
          }
          if (!enrolledModulesTableInfo.any((column) => column['name'] == columnModuleDownloadLink)) {
            await db.execute('ALTER TABLE $tableEnrolledModules ADD COLUMN $columnModuleDownloadLink TEXT');
          }
          if (!enrolledModulesTableInfo.any((column) => column['name'] == columnModuleScormDataPath)) {
            await db.execute('ALTER TABLE $tableEnrolledModules ADD COLUMN $columnModuleScormDataPath TEXT');
          }

          LoggerService.debug('Added new columns to enrolled_modules table in database upgrade');
        } catch (e) {
          LoggerService.error('Error adding new columns to enrolled_modules table', e);
        }
      }

      if (oldVersion < 8) {
        try {
          // Add module progress table for non-SCORM content
          await db.execute('''
            CREATE TABLE IF NOT EXISTS $tableModuleProgress (
              $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
              $columnUserId INTEGER NOT NULL,
              $columnModuleId INTEGER NOT NULL,
              $columnContentType TEXT NOT NULL,
              $columnCompletionStatus INTEGER DEFAULT 0,
              $columnProgressData TEXT,
              $columnLastAccessed TEXT NOT NULL,
              $columnSyncStatus INTEGER DEFAULT 0,
              $columnSyncAttempts INTEGER DEFAULT 0,
              $columnCreatedAt TEXT NOT NULL,
              $columnUpdatedAt TEXT NOT NULL,
              FOREIGN KEY ($columnUserId) REFERENCES $tableUsers ($columnId) ON DELETE CASCADE,
              FOREIGN KEY ($columnModuleId) REFERENCES $tableModules ($columnId) ON DELETE CASCADE,
              UNIQUE($columnUserId, $columnModuleId)
            )
          ''');

          LoggerService.debug('Added module progress table in database upgrade');
        } catch (e) {
          LoggerService.error('Error adding module progress table', e);
        }
      }

      if (oldVersion < 2) {
        // Add category column to library_items table
        try {
          // Check if the column already exists
          var tableInfo = await db.rawQuery("PRAGMA table_info($tableLibraryItems)");
          bool columnExists = tableInfo.any((column) => column['name'] == columnContentCategory);

          if (!columnExists) {
            await db.execute(
              'ALTER TABLE $tableLibraryItems ADD COLUMN $columnContentCategory TEXT'
            );
            LoggerService.debug('Added category column to library_items table');
          }
        } catch (e) {
          LoggerService.error('Error adding category column to library_items table', e);
        }
      }
    } catch (e) {
      LoggerService.error('Error upgrading database', e);
      rethrow;
    }
  }

  // Close the database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
