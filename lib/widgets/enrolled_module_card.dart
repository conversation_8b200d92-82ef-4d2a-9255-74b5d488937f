import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:open_filex/open_filex.dart';
import '../models/course_model.dart';
import '../providers/course_provider.dart';
import '../services/content_handler_service.dart';
import '../services/logger_service.dart';
import '../screens/courses/download_screen.dart';
import '../screens/courses/universal_download_screen.dart';
import '../screens/library/pdf_viewer_screen.dart';
import '../screens/media/media_player_screen.dart';
import '../theme/app_theme.dart';
import '../utils/responsive_utils.dart';
import '../localization/app_localizations_extension.dart';

class EnrolledModuleCard extends StatefulWidget {
  final EnrolledModule module;
  final String? courseSlug; // Course slug for proper download folder structure
  final IconData? iconData; // Optional, will be determined automatically
  final Color? iconColor; // Optional, will be determined automatically

  const EnrolledModuleCard({
    super.key,
    required this.module,
    this.courseSlug,
    this.iconData,
    this.iconColor,
  });

  @override
  State<EnrolledModuleCard> createState() => _EnrolledModuleCardState();
}

class _EnrolledModuleCardState extends State<EnrolledModuleCard> {
  bool _isOpening = false;
  bool _isDownloaded = false;
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    _checkDownloadStatus();
    _checkConnectivity();
  }

  /// Check if module is downloaded locally
  Future<void> _checkDownloadStatus() async {
    try {
      // Use iOS/Android compatible storage
      final appDocDir = await getApplicationDocumentsDirectory();
      final myCoursesDir = Directory('${appDocDir.path}/my_courses');
      bool exists = false;

      if (await myCoursesDir.exists()) {
        await for (var courseDir in myCoursesDir.list()) {
          if (courseDir is Directory) {
            final modulePath = '${courseDir.path}/module_${widget.module.id}';
            final moduleDir = Directory(modulePath);

            if (await moduleDir.exists()) {
              // Check content type to determine how to verify download
              if (widget.module.content?.isScorm == true && widget.module.content?.scormDataPath != null) {
                // For SCORM content, check if the main file exists
                final scormFile = File('$modulePath/${widget.module.content!.scormDataPath}');
                exists = await scormFile.exists();
              } else {
                // For PDF/Audio/Video content, check if any content file exists
                final files = await moduleDir.list().toList();
                exists = files.isNotEmpty;
              }

              if (exists) break; // Exit early if found
            }
          }
        }
      }

      if (mounted) {
        setState(() {
          _isDownloaded = exists;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking download status', e);
    }
  }

  /// Check connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (mounted) {
        setState(() {
          _isOnline = connectivityResult != ConnectivityResult.none;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking connectivity', e);
    }
  }

  /// Handle module tap with smart download/online options
  Future<void> _handleModuleTap() async {
    if (_isOpening) return;

    setState(() {
      _isOpening = true;
    });

    try {
      LoggerService.info('Enrolled module tapped: ${widget.module.id}');
      LoggerService.info('Content type: ${widget.module.content?.type}');
      LoggerService.info('Is downloaded: $_isDownloaded');
      LoggerService.info('Is online: $_isOnline');

      if (_isOnline) {
        if (_isDownloaded) {
          // Online & Downloaded -> Play offline version directly
          await _playOfflineContent();
        } else {
          // Online & Not Downloaded -> Show options (watch online or download)
          await _showDownloadOptions();
        }
      } else {
        if (_isDownloaded) {
          // Offline & Downloaded -> Play offline version
          await _playOfflineContent();
        } else {
          // Offline & Not Downloaded -> Show error
          _showOfflineError();
        }
      }
    } catch (e) {
      LoggerService.error('Error handling enrolled module tap', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOpening = false;
        });
      }
    }
  }

  /// Handle opening the enrolled module (legacy method for direct opening)
  Future<void> _openModule() async {
    if (_isOpening) return;

    setState(() {
      _isOpening = true;
    });

    try {
      LoggerService.info('Opening enrolled module: ${widget.module.id}');

      final success = await ContentHandlerService.openEnrolledModule(
        context,
        widget.module,
        courseSlug: widget.courseSlug,
        showErrorSnackbar: true,
      );

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('courses.module_not_available')),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Refresh download status after opening (in case module was downloaded)
      await _checkDownloadStatus();
      await _checkConnectivity();
    } catch (e) {
      LoggerService.error('Error opening enrolled module', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOpening = false;
        });
      }
    }
  }

  /// Play downloaded content offline based on content type
  Future<void> _playOfflineContent() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final myCoursesDir = Directory('${appDocDir.path}/my_courses');

      String? modulePath;
      if (await myCoursesDir.exists()) {
        await for (var courseDir in myCoursesDir.list()) {
          if (courseDir is Directory) {
            final path = '${courseDir.path}/module_${widget.module.id}';
            if (await Directory(path).exists()) {
              modulePath = path;
              break;
            }
          }
        }
      }

      if (modulePath != null) {
        if (widget.module.content?.isScorm == true && widget.module.content?.scormDataPath != null) {
          // SCORM content - use ContentHandlerService
          await ContentHandlerService.openEnrolledModule(
            context,
            widget.module,
            courseSlug: widget.courseSlug,
            showErrorSnackbar: true,
          );
        } else {
          // PDF, Audio, Video content - find and open the downloaded file
          final moduleDir = Directory(modulePath);
          final files = await moduleDir.list().toList();
          if (files.isNotEmpty) {
            final file = files.first;
            if (file is File) {
              await _openDownloadedFile(file.path);
            }
          }
        }
      }
    } catch (e) {
      LoggerService.error('Error playing offline content', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing offline content: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Open downloaded file using appropriate viewer
  Future<void> _openDownloadedFile(String filePath) async {
    try {
      final extension = filePath.split('.').last.toLowerCase();

      if (extension == 'pdf') {
        // Open PDF viewer
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFViewerScreen(
              pdfUrl: filePath,
              title: 'Module ${widget.module.id}',
            ),
          ),
        );
      } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension) ||
                 ['mp3', 'wav', 'aac', 'm4a', 'ogg'].contains(extension)) {
        // Open media player
        final isVideo = ['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MediaPlayerScreen(
              mediaUrl: filePath,
              title: 'Module ${widget.module.id}',
              isVideo: isVideo,
            ),
          ),
        );
      } else {
        // Try to open with system default app
        await OpenFilex.open(filePath);
      }
    } catch (e) {
      LoggerService.error('Error opening downloaded file', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening file: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show download options (watch online or download)
  Future<void> _showDownloadOptions() async {
    try {
      final result = await showModalBottomSheet<bool>(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        ),
        builder: (context) => SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.play_circle),
                title: Text(context.tr('courses.watch_online')),
                onTap: () {
                  Navigator.pop(context, true);
                },
              ),
              ListTile(
                leading: const Icon(Icons.download),
                title: Text(context.tr('courses.download')),
                onTap: () {
                  Navigator.pop(context, false);
                },
              ),
            ],
          ),
        ),
      );

      if (result == true) {
        // Watch online
        await _openModule();
      } else if (result == false) {
        // Download module
        await _downloadModule();
      }
    } catch (e) {
      LoggerService.error('Error showing download options', e);
    }
  }

  /// Download module for offline use
  Future<void> _downloadModule() async {
    try {
      if (widget.module.content?.isScorm == true) {
        // SCORM content - use existing DownloadPage
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DownloadPage(
              courseSlug: widget.courseSlug ?? 'enrolled_course_${widget.module.id}',
              moduleSlug: 'module_${widget.module.id}',
              downloadLink: widget.module.content!.downloadLink,
            ),
          ),
        ).then((_) => _checkDownloadStatus());
      } else {
        // PDF, Audio, Video content - use universal download
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UniversalDownloadPage(
              courseSlug: widget.courseSlug ?? 'enrolled_course_${widget.module.id}',
              moduleSlug: 'module_${widget.module.id}',
              downloadLink: widget.module.content!.downloadLink,
              moduleName: 'Module ${widget.module.id}',
            ),
          ),
        ).then((_) => _checkDownloadStatus());
      }
    } catch (e) {
      LoggerService.error('Error downloading module', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show offline error dialog
  void _showOfflineError() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline'),
        content: const Text('You are offline and this content is not available for offline viewing.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Get content type display text
  String _getContentTypeText() {
    if (widget.module.content == null) return 'Content';
    
    final content = widget.module.content!;
    if (content.isPdf) return 'PDF';
    if (content.isAudioVideo) return 'Video';
    if (content.isScorm) return 'Interactive';
    return 'Content';
  }

  /// Get content type icon
  IconData _getContentTypeIcon() {
    if (widget.module.content == null) return Icons.description;

    final content = widget.module.content!;
    if (content.isPdf) return Icons.picture_as_pdf;
    if (content.isAudioVideo) return Icons.play_circle;
    if (content.isScorm) {
      // Show different icons based on download status for SCORM
      if (_isDownloaded) {
        return Icons.offline_pin; // Downloaded
      } else {
        return Icons.rocket; // Not downloaded
      }
    }
    return Icons.description;
  }

  /// Get action button icon based on module state and download status
  IconData _getActionIcon() {
    if (!widget.module.accessible) {
      return Icons.lock;
    }

    if (widget.module.completionStatus) {
      return Icons.check_circle;
    }

    // For SCORM content, show download-aware icons
    if (widget.module.content?.isScorm == true) {
      if (_isDownloaded) {
        return Icons.play_circle; // Can play offline
      } else if (_isOnline) {
        return Icons.cloud_download; // Can download or play online
      } else {
        return Icons.cloud_off; // Offline, no download available
      }
    }

    return Icons.play_circle;
  }

  /// Get action button color
  Color _getActionColor(BuildContext context) {
    if (!widget.module.accessible) {
      return Colors.red;
    }

    if (widget.module.completionStatus) {
      return Colors.green;
    }

    // For SCORM content
    if (widget.module.content?.isScorm == true) {
      if (_isDownloaded) {
        return Colors.green; // Downloaded and ready
      } else if (_isOnline) {
        return Theme.of(context).colorScheme.primary; // Online, can download
      } else {
        return Colors.grey; // Offline, not available
      }
    }

    return Theme.of(context).colorScheme.primary;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    // Responsive sizing
    final padding = EdgeInsets.all(
      ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 16.0,
        tablet: 18.0,
        desktop: 20.0,
      ),
    );

    final borderRadius = ResponsiveUtils.getValueForDeviceType<double>(
      context: context,
      mobile: AppTheme.borderRadiusSmall,
      tablet: AppTheme.borderRadiusMedium,
      desktop: AppTheme.borderRadiusLarge,
    );

    final titleStyle = theme.textTheme.titleMedium?.copyWith(
      fontWeight: FontWeight.w600,
      fontSize: ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 16.0,
        tablet: 17.0,
        desktop: 18.0,
      ),
    );

    final subtitleStyle = theme.textTheme.bodyMedium?.copyWith(
      color: theme.colorScheme.onSurface.withOpacity(0.7),
      fontSize: ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 13.0,
        tablet: 14.0,
        desktop: 15.0,
      ),
    );

    final iconSize = ResponsiveUtils.getValueForDeviceType<double>(
      context: context,
      mobile: 24.0,
      tablet: 26.0,
      desktop: 28.0,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: InkWell(
            onTap: widget.module.accessible && !_isOpening ? _handleModuleTap : null,
            borderRadius: BorderRadius.circular(borderRadius),
            child: Padding(
              padding: padding,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Content type icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getContentTypeIcon(),
                      size: 20,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Content section (title and description)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Module title
                        Text(
                          'Module ${widget.module.id}',
                          style: titleStyle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),
                        
                        // Content type and accessibility info
                        Row(
                          children: [
                            Text(
                              _getContentTypeText(),
                              style: subtitleStyle,
                            ),
                            if (!widget.module.accessible) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.lock,
                                size: 14,
                                color: Colors.red,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Locked',
                                style: subtitleStyle?.copyWith(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),

                        // Dependent modules info
                        if (widget.module.dependentModuleList.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'Requires: ${widget.module.dependentModuleList.join(", ")}',
                              style: subtitleStyle?.copyWith(
                                fontSize: 11,
                                fontStyle: FontStyle.italic,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Action button
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusCircular),
                    ),
                    child: _isOpening
                        ? Padding(
                            padding: const EdgeInsets.all(12),
                            child: SizedBox(
                              width: iconSize,
                              height: iconSize,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          )
                        : IconButton(
                            icon: Icon(
                              _getActionIcon(),
                              size: iconSize,
                              color: _getActionColor(context),
                            ),
                            onPressed: widget.module.accessible && !_isOpening ? _openModule : null,
                            tooltip: widget.module.accessible
                                ? context.tr('courses.open_module')
                                : context.tr('courses.module_locked'),
                          ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
