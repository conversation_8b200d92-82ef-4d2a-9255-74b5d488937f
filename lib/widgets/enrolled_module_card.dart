import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/course_model.dart';
import '../providers/course_provider.dart';
import '../services/content_handler_service.dart';
import '../services/logger_service.dart';
import '../theme/app_theme.dart';
import '../utils/responsive_utils.dart';
import '../localization/app_localizations_extension.dart';

class EnrolledModuleCard extends StatefulWidget {
  final EnrolledModule module;
  final String? courseSlug; // Course slug for proper download folder structure
  final IconData? iconData; // Optional, will be determined automatically
  final Color? iconColor; // Optional, will be determined automatically

  const EnrolledModuleCard({
    super.key,
    required this.module,
    this.courseSlug,
    this.iconData,
    this.iconColor,
  });

  @override
  State<EnrolledModuleCard> createState() => _EnrolledModuleCardState();
}

class _EnrolledModuleCardState extends State<EnrolledModuleCard> {
  bool _isOpening = false;
  bool _isDownloaded = false;
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    _checkDownloadStatus();
    _checkConnectivity();
  }

  /// Check if module is downloaded locally
  Future<void> _checkDownloadStatus() async {
    if (widget.module.content?.isScorm == true) {
      try {
        // Use iOS/Android compatible storage
        final appDocDir = await getApplicationDocumentsDirectory();
        final myCoursesDir = Directory('${appDocDir.path}/my_courses');
        bool exists = false;

        if (await myCoursesDir.exists()) {
          await for (var courseDir in myCoursesDir.list()) {
            if (courseDir is Directory) {
              final modulePath = '${courseDir.path}/module_${widget.module.id}';
              if (await Directory(modulePath).exists()) {
                exists = true;
                break;
              }
            }
          }
        }

        if (mounted) {
          setState(() {
            _isDownloaded = exists;
          });
        }
      } catch (e) {
        LoggerService.error('Error checking download status', e);
      }
    }
  }

  /// Check connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (mounted) {
        setState(() {
          _isOnline = connectivityResult != ConnectivityResult.none;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking connectivity', e);
    }
  }

  /// Handle opening the enrolled module
  Future<void> _openModule() async {
    if (_isOpening) return;

    setState(() {
      _isOpening = true;
    });

    try {
      LoggerService.info('Opening enrolled module: ${widget.module.id}');

      final success = await ContentHandlerService.openEnrolledModule(
        context,
        widget.module,
        courseSlug: widget.courseSlug,
        showErrorSnackbar: true,
      );

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('courses.module_not_available')),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Refresh download status after opening (in case module was downloaded)
      await _checkDownloadStatus();
      await _checkConnectivity();
    } catch (e) {
      LoggerService.error('Error opening enrolled module', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOpening = false;
        });
      }
    }
  }

  /// Get content type display text
  String _getContentTypeText() {
    if (widget.module.content == null) return 'Content';
    
    final content = widget.module.content!;
    if (content.isPdf) return 'PDF';
    if (content.isAudioVideo) return 'Video';
    if (content.isScorm) return 'Interactive';
    return 'Content';
  }

  /// Get content type icon
  IconData _getContentTypeIcon() {
    if (widget.module.content == null) return Icons.description;

    final content = widget.module.content!;
    if (content.isPdf) return Icons.picture_as_pdf;
    if (content.isAudioVideo) return Icons.play_circle;
    if (content.isScorm) {
      // Show different icons based on download status for SCORM
      if (_isDownloaded) {
        return Icons.offline_pin; // Downloaded
      } else {
        return Icons.rocket; // Not downloaded
      }
    }
    return Icons.description;
  }

  /// Get action button icon based on module state and download status
  IconData _getActionIcon() {
    if (!widget.module.accessible) {
      return Icons.lock;
    }

    if (widget.module.completionStatus) {
      return Icons.check_circle;
    }

    // For SCORM content, show download-aware icons
    if (widget.module.content?.isScorm == true) {
      if (_isDownloaded) {
        return Icons.play_circle; // Can play offline
      } else if (_isOnline) {
        return Icons.cloud_download; // Can download or play online
      } else {
        return Icons.cloud_off; // Offline, no download available
      }
    }

    return Icons.play_circle;
  }

  /// Get action button color
  Color _getActionColor(BuildContext context) {
    if (!widget.module.accessible) {
      return Colors.red;
    }

    if (widget.module.completionStatus) {
      return Colors.green;
    }

    // For SCORM content
    if (widget.module.content?.isScorm == true) {
      if (_isDownloaded) {
        return Colors.green; // Downloaded and ready
      } else if (_isOnline) {
        return Theme.of(context).colorScheme.primary; // Online, can download
      } else {
        return Colors.grey; // Offline, not available
      }
    }

    return Theme.of(context).colorScheme.primary;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    // Responsive sizing
    final padding = EdgeInsets.all(
      ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 16.0,
        tablet: 18.0,
        desktop: 20.0,
      ),
    );

    final borderRadius = ResponsiveUtils.getValueForDeviceType<double>(
      context: context,
      mobile: AppTheme.borderRadiusSmall,
      tablet: AppTheme.borderRadiusMedium,
      desktop: AppTheme.borderRadiusLarge,
    );

    final titleStyle = theme.textTheme.titleMedium?.copyWith(
      fontWeight: FontWeight.w600,
      fontSize: ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 16.0,
        tablet: 17.0,
        desktop: 18.0,
      ),
    );

    final subtitleStyle = theme.textTheme.bodyMedium?.copyWith(
      color: theme.colorScheme.onSurface.withOpacity(0.7),
      fontSize: ResponsiveUtils.getValueForDeviceType<double>(
        context: context,
        mobile: 13.0,
        tablet: 14.0,
        desktop: 15.0,
      ),
    );

    final iconSize = ResponsiveUtils.getValueForDeviceType<double>(
      context: context,
      mobile: 24.0,
      tablet: 26.0,
      desktop: 28.0,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: InkWell(
            onTap: widget.module.accessible && !_isOpening ? _openModule : null,
            borderRadius: BorderRadius.circular(borderRadius),
            child: Padding(
              padding: padding,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Content type icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getContentTypeIcon(),
                      size: 20,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Content section (title and description)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Module title
                        Text(
                          'Module ${widget.module.id}',
                          style: titleStyle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),
                        
                        // Content type and accessibility info
                        Row(
                          children: [
                            Text(
                              _getContentTypeText(),
                              style: subtitleStyle,
                            ),
                            if (!widget.module.accessible) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.lock,
                                size: 14,
                                color: Colors.red,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Locked',
                                style: subtitleStyle?.copyWith(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),

                        // Dependent modules info
                        if (widget.module.dependentModuleList.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'Requires: ${widget.module.dependentModuleList.join(", ")}',
                              style: subtitleStyle?.copyWith(
                                fontSize: 11,
                                fontStyle: FontStyle.italic,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Action button
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusCircular),
                    ),
                    child: _isOpening
                        ? Padding(
                            padding: const EdgeInsets.all(12),
                            child: SizedBox(
                              width: iconSize,
                              height: iconSize,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          )
                        : IconButton(
                            icon: Icon(
                              _getActionIcon(),
                              size: iconSize,
                              color: _getActionColor(context),
                            ),
                            onPressed: widget.module.accessible && !_isOpening ? _openModule : null,
                            tooltip: widget.module.accessible
                                ? context.tr('courses.open_module')
                                : context.tr('courses.module_locked'),
                          ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
