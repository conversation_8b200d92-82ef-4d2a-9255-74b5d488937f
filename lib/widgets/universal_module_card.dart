import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_filex/open_filex.dart';
import '../models/course_model.dart';
import '../screens/courses/download_screen.dart';
import '../screens/courses/universal_download_screen.dart';
import '../screens/library/pdf_viewer_screen.dart';
import '../screens/media/media_player_screen.dart';
import '../services/content_handler_service.dart';
import '../services/logger_service.dart';
import '../theme/app_theme.dart';
import '../theme/app_theme_extensions.dart';
import '../utils/responsive_utils.dart';
import '../utils/scorm_player.dart';
import 'package:ZABAI/localization/app_localizations_extension.dart';

class UniversalModuleCard extends StatefulWidget {
  // Common properties
  final String courseSlug;
  final VoidCallback? onWatch;
  final IconData? iconData;
  final Color? iconColor;
  
  // Regular module properties
  final Module? module;
  
  // Enrolled module properties  
  final EnrolledModule? enrolledModule;
  final String? enrolledCourseSlug;

  const UniversalModuleCard({
    super.key,
    required this.courseSlug,
    this.onWatch,
    this.iconData,
    this.iconColor,
    this.module,
    this.enrolledModule,
    this.enrolledCourseSlug,
  }) : assert(
         (module != null && enrolledModule == null) ||
         (module == null && enrolledModule != null),
         'Either module or enrolledModule must be provided, but not both'
       );

  // Factory constructors for clarity
  factory UniversalModuleCard.regular({
    required Module module,
    required String courseSlug,
    required VoidCallback onWatch,
    IconData? iconData,
    Color? iconColor,
  }) {
    return UniversalModuleCard(
      module: module,
      courseSlug: courseSlug,
      onWatch: onWatch,
      iconData: iconData,
      iconColor: iconColor,
    );
  }

  factory UniversalModuleCard.enrolled({
    required EnrolledModule enrolledModule,
    required String courseSlug,
    String? enrolledCourseSlug,
    IconData? iconData,
    Color? iconColor,
  }) {
    return UniversalModuleCard(
      enrolledModule: enrolledModule,
      courseSlug: courseSlug,
      enrolledCourseSlug: enrolledCourseSlug,
      iconData: iconData,
      iconColor: iconColor,
    );
  }

  @override
  State<UniversalModuleCard> createState() => _UniversalModuleCardState();
}

class _UniversalModuleCardState extends State<UniversalModuleCard> {
  bool _fileExists = false;
  bool _isDownloaded = false;
  bool _isOnline = false;
  bool _isOpening = false;

  @override
  void initState() {
    super.initState();
    _checkFileExistence();
    _checkDownloadStatus();
    _checkConnectivity();
  }

  // Unified getters for module properties
  String get _moduleName => widget.module?.name ?? 'Module ${widget.enrolledModule?.id}';
  int get _moduleId => widget.module?.id ?? widget.enrolledModule?.id ?? 0;
  String get _moduleSlug => widget.module?.moduleSlug ?? 'module_${widget.enrolledModule?.id}';
  String get _downloadLink => widget.module?.downloadLink ?? widget.enrolledModule?.content?.downloadLink ?? '';
  String get _scormDataPath => widget.module?.scormDataPath ?? widget.enrolledModule?.content?.scormDataPath ?? '';
  String? get _contentType => widget.module?.contentType ?? widget.enrolledModule?.content?.type;
  bool get _isAccessible => widget.module?.accessible ?? widget.enrolledModule?.accessible ?? true;
  bool get _isCompleted => widget.enrolledModule?.completionStatus ?? false;
  bool get _isScorm => _contentType?.toUpperCase() == 'SCORM';

  String get _modulePath {
    return '/my_courses/${widget.courseSlug}/$_moduleSlug';
  }

  Future<void> _checkFileExistence() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final folderPath = '${appDocDir.path}$_modulePath';

      if (kDebugMode) {
        print('Checking file existence for module: $_moduleName');
        print('Folder path: $folderPath');
        print('Content type: $_contentType');
      }

      bool exists = false;

      if (await Directory(folderPath).exists()) {
        if (_isScorm && _scormDataPath.isNotEmpty) {
          final scormFile = File('$folderPath/$_scormDataPath');
          exists = await scormFile.exists();
        } else {
          final moduleDir = Directory(folderPath);
          final files = await moduleDir.list().toList();
          exists = files.isNotEmpty;
        }
      }

      if (mounted) {
        setState(() {
          _fileExists = exists;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking file existence: $e');
      }
      if (mounted) {
        setState(() {
          _fileExists = false;
        });
      }
    }
  }

  Future<void> _checkDownloadStatus() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final myCoursesDir = Directory('${appDocDir.path}/my_courses');
      bool exists = false;

      if (await myCoursesDir.exists()) {
        await for (var courseDir in myCoursesDir.list()) {
          if (courseDir is Directory) {
            final modulePath = '${courseDir.path}/$_moduleSlug';
            final moduleDir = Directory(modulePath);
            
            if (await moduleDir.exists()) {
              if (_isScorm && _scormDataPath.isNotEmpty) {
                final scormFile = File('$modulePath/$_scormDataPath');
                exists = await scormFile.exists();
              } else {
                final files = await moduleDir.list().toList();
                exists = files.isNotEmpty;
              }
              
              if (exists) break;
            }
          }
        }
      }

      if (mounted) {
        setState(() {
          _isDownloaded = exists;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking download status', e);
    }
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (mounted) {
        setState(() {
          _isOnline = connectivityResult != ConnectivityResult.none;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking connectivity', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);

    final double elevation = ResponsiveUtils.getValueForDeviceType(
      context: context,
      mobile: AppTheme.elevationSmall,
      tablet: AppTheme.elevationMedium,
      desktop: AppTheme.elevationMedium,
    );

    final double borderRadius = ResponsiveUtils.getValueForDeviceType(
      context: context,
      mobile: AppTheme.borderRadiusMedium,
      tablet: AppTheme.borderRadiusLarge,
      desktop: AppTheme.borderRadiusLarge,
    );

    final EdgeInsets padding = ResponsiveUtils.getValueForDeviceType(
      context: context,
      mobile: const EdgeInsets.all(12.0),
      tablet: const EdgeInsets.all(16.0),
      desktop: const EdgeInsets.all(20.0),
    );

    return Card(
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: InkWell(
        onTap: _isAccessible && !_isOpening ? _handleModuleTap : null,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: padding,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Content type icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getContentTypeIcon(),
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              
              // Module info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _moduleName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getContentTypeText(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action button
              if (_isOpening)
                const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                IconButton(
                  icon: Icon(
                    _getActionIcon(),
                    color: _getActionColor(context),
                  ),
                  onPressed: _isAccessible && !_isOpening ? _handleModuleTap : null,
                  tooltip: _isAccessible 
                      ? context.tr('courses.open_module')
                      : context.tr('courses.module_locked'),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle module tap with smart download/online options
  Future<void> _handleModuleTap() async {
    if (_isOpening) return;

    setState(() {
      _isOpening = true;
    });

    try {
      LoggerService.info('Module tapped: $_moduleName');
      LoggerService.info('Content type: $_contentType');
      LoggerService.info('Is downloaded: $_isDownloaded');
      LoggerService.info('Is online: $_isOnline');

      // Downloaded content always plays offline (regardless of connectivity)
      if (_isDownloaded) {
        await _playOfflineContent();
      } else if (_isOnline) {
        // Not downloaded & online -> show options
        await _showDownloadOptions();
      } else {
        // Not downloaded & offline -> show error
        _showOfflineError();
      }
    } catch (e) {
      LoggerService.error('Error handling module tap', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening module: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOpening = false;
        });
        // Refresh status after action
        await _checkDownloadStatus();
        await _checkConnectivity();
      }
    }
  }

  /// Play downloaded content offline based on content type
  Future<void> _playOfflineContent() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final modulePath = '${appDocDir.path}$_modulePath';

      if (_isScorm && _scormDataPath.isNotEmpty) {
        // SCORM content
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ScormPlayer(
              scormDataPath: '${_modulePath.substring(1)}/$_scormDataPath',
              moduleName: _moduleName,
              moduleId: _moduleId,
              courseId: _moduleId,
            ),
          ),
        );
      } else {
        // PDF, Audio, Video content - find the downloaded file
        final moduleDir = Directory(modulePath);
        if (await moduleDir.exists()) {
          final files = await moduleDir.list().toList();
          if (files.isNotEmpty) {
            final file = files.first;
            if (file is File) {
              await _openDownloadedFile(file.path);
            }
          }
        }
      }
    } catch (e) {
      LoggerService.error('Error playing offline content', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing offline content: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Open downloaded file using appropriate viewer
  Future<void> _openDownloadedFile(String filePath) async {
    try {
      final extension = filePath.split('.').last.toLowerCase();

      if (extension == 'pdf') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFViewerScreen(
              pdfUrl: filePath,
              title: _moduleName,
            ),
          ),
        );
      } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension) ||
                 ['mp3', 'wav', 'aac', 'm4a', 'ogg'].contains(extension)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MediaPlayerScreen(
              mediaUrl: filePath,
              title: _moduleName,
              isVideo: ['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension),
            ),
          ),
        );
      } else {
        await OpenFilex.open(filePath);
      }
    } catch (e) {
      LoggerService.error('Error opening downloaded file', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening file: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show download options (watch online or download)
  Future<void> _showDownloadOptions() async {
    try {
      final result = await showModalBottomSheet<bool>(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        ),
        builder: (context) => SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.play_circle),
                title: Text(context.tr('courses.watch_online')),
                onTap: () {
                  Navigator.pop(context, true);
                },
              ),
              ListTile(
                leading: const Icon(Icons.download),
                title: Text(context.tr('courses.download')),
                onTap: () {
                  Navigator.pop(context, false);
                },
              ),
            ],
          ),
        ),
      );

      if (result == true) {
        // Watch online
        await _watchOnline();
      } else if (result == false) {
        // Download module
        await _downloadContent();
      }
    } catch (e) {
      LoggerService.error('Error showing download options', e);
    }
  }

  /// Watch content online
  Future<void> _watchOnline() async {
    try {
      LoggerService.info('Watch online called for module: $_moduleName');

      if (widget.module != null) {
        // Regular module - use provided onWatch callback
        LoggerService.info('Using onWatch callback for regular module');
        if (widget.onWatch != null) {
          widget.onWatch!();
        } else {
          LoggerService.error('onWatch callback is null for regular module');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Error: Watch online functionality not configured'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else if (widget.enrolledModule != null) {
        // Enrolled module - use ContentHandlerService
        LoggerService.info('Using ContentHandlerService for enrolled module');
        LoggerService.info('Content type: ${widget.enrolledModule!.content?.type}');
        LoggerService.info('Download link: ${widget.enrolledModule!.content?.downloadLink}');

        final success = await ContentHandlerService.openEnrolledModule(
          context,
          widget.enrolledModule!,
          courseSlug: widget.enrolledCourseSlug,
          showErrorSnackbar: true,
        );

        LoggerService.info('ContentHandlerService result: $success');
      } else {
        LoggerService.warning('No module or enrolledModule found');
      }
    } catch (e) {
      LoggerService.error('Error watching online', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error watching online: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Download content based on content type
  Future<void> _downloadContent() async {
    try {
      if (_isScorm && _scormDataPath.isNotEmpty) {
        // SCORM content - use existing DownloadPage
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DownloadPage(
              courseSlug: widget.courseSlug,
              moduleSlug: _moduleSlug,
              downloadLink: _downloadLink,
            ),
          ),
        ).then((_) => _checkFileExistence());
      } else {
        // PDF, Audio, Video content - use universal download
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UniversalDownloadPage(
              courseSlug: widget.courseSlug,
              moduleSlug: _moduleSlug,
              downloadLink: _downloadLink,
              moduleName: _moduleName,
            ),
          ),
        ).then((_) => _checkFileExistence());
      }
    } catch (e) {
      LoggerService.error('Error downloading content', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading content: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show offline error dialog
  void _showOfflineError() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline'),
        content: const Text('You are offline and this content is not available for offline viewing.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Get content type icon
  IconData _getContentTypeIcon() {
    if (_contentType == null) return Icons.description;

    final type = _contentType!.toUpperCase();
    if (type == 'PDF') return Icons.picture_as_pdf;
    if (type == 'AUDIO / VIDEO') return Icons.play_circle;
    if (type == 'SCORM') {
      if (_isDownloaded) {
        return Icons.offline_pin; // Downloaded
      } else {
        return Icons.rocket; // Not downloaded
      }
    }
    return Icons.description;
  }

  /// Get content type display text
  String _getContentTypeText() {
    if (_contentType == null) return 'Content';

    final type = _contentType!.toUpperCase();
    if (type == 'PDF') return 'PDF';
    if (type == 'AUDIO / VIDEO') return 'Video';
    if (type == 'SCORM') return 'Interactive';
    return 'Content';
  }

  /// Get action button icon based on module state and download status
  IconData _getActionIcon() {
    if (!_isAccessible) {
      return Icons.lock;
    }

    if (_isCompleted) {
      return Icons.check_circle;
    }

    // For any content type, show download-aware icons
    if (_isDownloaded) {
      return Icons.play_circle; // Can play offline
    } else if (_isOnline) {
      return Icons.cloud_download; // Can download or play online
    } else {
      return Icons.cloud_off; // Offline, no download available
    }
  }

  /// Get action button color
  Color _getActionColor(BuildContext context) {
    if (!_isAccessible) {
      return Colors.red;
    }

    if (_isCompleted) {
      return Colors.green;
    }

    // For any content type
    if (_isDownloaded) {
      return Colors.green; // Downloaded and ready
    } else if (_isOnline) {
      return Theme.of(context).colorScheme.primary; // Online, can download
    } else {
      return Colors.grey; // Offline, not available
    }
  }
}
