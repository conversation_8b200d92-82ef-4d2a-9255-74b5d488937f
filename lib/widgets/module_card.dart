import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_filex/open_filex.dart';
import '../models/course_model.dart';
import '../screens/courses/download_screen.dart';
import '../screens/courses/universal_download_screen.dart';
import '../screens/library/pdf_viewer_screen.dart';
import '../screens/media/media_player_screen.dart';
import '../theme/app_theme.dart';
import '../theme/app_theme_extensions.dart';
import '../utils/responsive_utils.dart';
import 'package:ZABAI/localization/app_localizations_extension.dart';

import '../utils/scorm_player.dart';

class ModuleCard extends StatefulWidget {
  final Module module;
  final String courseSlug;
  final VoidCallback onWatch;
  final IconData iconData;
  final Color? iconColor;

  const ModuleCard({
    super.key,
    required this.module,
    required this.courseSlug,
    required this.onWatch,
    required this.iconData,
    required this.iconColor,
  });

  @override
  State<ModuleCard> createState() => _ModuleCardState();
}

class _ModuleCardState extends State<ModuleCard> {
  late bool _fileExists = false;

  @override
  void initState() {
    super.initState();
    _checkFileExistence();
  }

  // Memoized path calculation
  String get _modulePath {
    return '/my_courses/${widget.courseSlug}/${widget.module.moduleSlug}';
  }

  Future<void> _checkFileExistence() async {
    try {
      // Use iOS/Android compatible storage
      final appDocDir = await getApplicationDocumentsDirectory();
      final folderPath = '${appDocDir.path}$_modulePath';

      if (kDebugMode) {
        print('Checking file existence for module: ${widget.module.name}');
        print('Folder path: $folderPath');
        print('SCORM data path: ${widget.module.scormDataPath}');
      }

      bool exists = false;

      // Check if the module folder exists
      if (await Directory(folderPath).exists()) {
        if (kDebugMode) {
          print('Module folder exists');
        }

        // Check content type to determine how to verify download
        final contentType = widget.module.contentType?.toUpperCase() ?? '';

        if (contentType == 'SCORM' && widget.module.scormDataPath.isNotEmpty) {
          // For SCORM content, check if the main file exists
          final scormFile = File('$folderPath/${widget.module.scormDataPath}');
          exists = await scormFile.exists();
          if (kDebugMode) {
            print('SCORM file exists: $exists');
          }
        } else {
          // For PDF/Audio/Video content, check if any content file exists
          final moduleDir = Directory(folderPath);
          final files = await moduleDir.list().toList();
          exists = files.isNotEmpty;
          if (kDebugMode) {
            print('Non-SCORM files count: ${files.length}');
            print('Content type: $contentType');
          }
        }
      } else {
        if (kDebugMode) {
          print('Module folder does not exist');
        }
      }

      if (mounted) {
        setState(() {
          _fileExists = exists;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking file existence: $e');
      }
      if (mounted) {
        setState(() {
          _fileExists = false;
        });
      }
    }
  }

  /// Play downloaded content offline based on content type
  Future<void> _playOfflineContent() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final modulePath = '${appDocDir.path}$_modulePath';

      // Check content type to determine how to play
      final contentType = widget.module.contentType?.toUpperCase() ?? '';

      if (contentType == 'SCORM' && widget.module.scormDataPath.isNotEmpty) {
        // SCORM content
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ScormPlayer(
              scormDataPath: '${_modulePath.substring(1)}/${widget.module.scormDataPath}',
              moduleName: widget.module.name,
              moduleId: widget.module.id,
              courseId: widget.module.id
            ),
          ),
        );
      } else {
        // PDF, Audio, Video content - find the downloaded file
        final moduleDir = Directory(modulePath);
        if (await moduleDir.exists()) {
          final files = await moduleDir.list().toList();
          if (files.isNotEmpty) {
            final file = files.first;
            if (file is File) {
              // Use ContentHandlerService to open the file
              await _openDownloadedFile(file.path);
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing offline content: $e');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing offline content: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Open downloaded file using appropriate viewer
  Future<void> _openDownloadedFile(String filePath) async {
    try {
      final extension = filePath.split('.').last.toLowerCase();

      if (extension == 'pdf') {
        // Open PDF viewer
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFViewerScreen(
              pdfUrl: filePath,
              title: widget.module.name,
            ),
          ),
        );
      } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension) ||
                 ['mp3', 'wav', 'aac', 'm4a', 'ogg'].contains(extension)) {
        // Open media player
        final isVideo = ['mp4', 'avi', 'mov', 'mkv', 'webm'].contains(extension);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MediaPlayerScreen(
              mediaUrl: filePath,
              title: widget.module.name,
              isVideo: isVideo,
            ),
          ),
        );
      } else {
        // Try to open with system default app
        await OpenFilex.open(filePath);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening downloaded file: $e');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening file: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Download content based on content type
  Future<void> _downloadContent() async {
    try {
      // Check content type to determine download method
      final contentType = widget.module.contentType?.toUpperCase() ?? '';

      if (contentType == 'SCORM' && widget.module.scormDataPath.isNotEmpty) {
        // SCORM content - use existing DownloadPage
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DownloadPage(
              courseSlug: widget.courseSlug,
              moduleSlug: widget.module.moduleSlug,
              downloadLink: widget.module.downloadLink,
            ),
          ),
        ).then((_) => _checkFileExistence());
      } else {
        // PDF, Audio, Video content - use new universal download
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UniversalDownloadPage(
              courseSlug: widget.courseSlug,
              moduleSlug: widget.module.moduleSlug,
              downloadLink: widget.module.downloadLink,
              moduleName: widget.module.name,
            ),
          ),
        ).then((_) => _checkFileExistence());
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading content: $e');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error downloading content: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final theme = Theme.of(context);
        final deviceType = ResponsiveUtils.getDeviceType(context);

        // Responsive values based on device type
        final double elevation = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.elevationSmall,
          tablet: AppTheme.elevationMedium,
          desktop: AppTheme.elevationMedium,
        );

        final double borderRadius = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.borderRadiusMedium,
          tablet: AppTheme.borderRadiusLarge,
          desktop: AppTheme.borderRadiusLarge,
        );

        final EdgeInsets padding = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: const EdgeInsets.all(12.0),
          tablet: const EdgeInsets.all(16.0),
          desktop: const EdgeInsets.all(20.0),
        );

        final double titleFontSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeMedium,
          tablet: AppTheme.fontSizeLarge,
          desktop: AppTheme.fontSizeXLarge,
        );

        final double descriptionFontSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeSmall,
          tablet: AppTheme.fontSizeRegular,
          desktop: AppTheme.fontSizeMedium,
        );

        final double iconSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: 20.0,
          tablet: 24.0,
          desktop: 28.0,
        );

        // Create a custom title style based on the responsive font size
        final TextStyle titleStyle = theme.cardTitle.copyWith(
          fontSize: titleFontSize,
        );

        // Create a custom subtitle style based on the responsive font size
        final TextStyle subtitleStyle = theme.cardSubtitle.copyWith(
          fontSize: descriptionFontSize,
        );

        return Card(
          margin: EdgeInsets.zero,
          elevation: elevation,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: InkWell(
            onTap: () async {
              if (kDebugMode) {
                print('Module tapped: ${widget.module.name}');
                print('Content type: ${widget.module.contentType}');
                print('SCORM data path: ${widget.module.scormDataPath}');
                print('Download link: ${widget.module.downloadLink}');
                print('File exists: $_fileExists');
              }

              final connectivityResult = await Connectivity().checkConnectivity();
              final isOnline = connectivityResult != ConnectivityResult.none;

              if (kDebugMode) {
                print('Is online: $isOnline');
              }

              if (isOnline) {
                if (_fileExists) {
                  // Online & File Exists -> play offline version (downloaded content)
                  await _playOfflineContent();
                } else {
                  // Online & No File -> show bottom sheet with options for all content types
                  if (kDebugMode) {
                    print('Showing download options for module: ${widget.module.name}');
                  }
                  showModalBottomSheet(
                    context: context,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
                    ),
                    builder: (context) => SafeArea(
                      child: Wrap(
                        children: [
                          ListTile(
                            leading: const Icon(Icons.play_circle),
                            title: Text(context.tr('courses.watch_online')),
                            onTap: () {
                              Navigator.pop(context);
                              widget.onWatch();
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.download),
                            title: Text(context.tr('courses.download')),
                            onTap: () {
                              Navigator.pop(context);
                              _downloadContent();
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                }
              } else {
                if (_fileExists) {
                  // Offline & File Exists -> play offline version
                  await _playOfflineContent();
                } else {
                  // Offline & No File -> show error
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Offline'),
                      content: const Text('You are offline and this content is not available.'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                }
              }
            },
            borderRadius: BorderRadius.circular(borderRadius),
            child: Padding(
              padding: padding,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Content section (title and description)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Module title
                        Text(
                          widget.module.name,
                          style: titleStyle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // Module description (if available)
                        if (widget.module.description.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              widget.module.description,
                              style: subtitleStyle,
                              maxLines: deviceType == DeviceType.mobile ? 2 : 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Eye icon button
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusCircular),
                    ),
                    child: IconButton(
                      icon: Icon(
                        widget.iconData,
                        size: iconSize,
                        color: widget.iconColor, // ✅ explicitly set the color here
                      ),
                      onPressed: widget.onWatch,
                      tooltip: context.tr('courses.watch_online'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}


